const dgram = require('dgram');
const EventEmitter = require('events');

class UDPService extends EventEmitter {
    constructor() {
        super();
        this.UDP_PORT = 5000; // Default UDP port
        this.LOCAL_UDP_PORT = 5001; // Local UDP port for receiving
        this.client = null;
        this.isScanning = false;
    }

    /**
     * Initialize UDP client
     */
    initialize() {
        this.client = dgram.createSocket('udp4');

        this.client.on('error', (err) => {
            console.error(`UDP Client error:\n${err.stack}`);
            this.client.close();
            this.emit('error', err);
        });

        this.client.on('message', (msg, rinfo) => {
            this._handleResponse(msg, rinfo);
        });

        this.client.bind(this.LOCAL_UDP_PORT);
    }

    /**
     * Close UDP client
     */
    close() {
        if (this.client) {
            this.client.close();
            this.client = null;
        }
    }

    /**
     * Calculate CRC for message
     * @param {Buffer} data - Message data
     * @param {number} start - Start index
     * @param {number} length - Length of data to calculate CRC
     * @returns {number} - CRC value
     */
    _calculateCRC(data, start, length) {
        let sumCRC = 0;
        for (let i = start; i < start + length; i++) {
            sumCRC += data[i];
        }
        return sumCRC;
    }

    /**
     * Create hardware request message
     * @param {string} id - Unit ID (format: x.x.x.x)
     * @returns {Buffer} - Message buffer
     */
    _createHardwareRequest(id) {
        const idParts = id.split('.');
        const data = Buffer.alloc(1024);

        // Address bytes
        data[0] = parseInt(idParts[3] || '0');
        data[1] = parseInt(idParts[2] || '0');
        data[2] = parseInt(idParts[1] || '0');
        data[3] = parseInt(idParts[0] || '0');

        // Length
        data[4] = 4;  // Address:4 ;Length=2 byte; CMD:2, Data:0, CRC:2
        data[5] = 0;

        // Command
        data[6] = 1;  // Request hardware info
        data[7] = 4;

        // Calculate CRC
        const sumCRC = this._calculateCRC(data, 4, data[4] + 2);
        data[9] = Math.floor(sumCRC / 256);
        data[8] = sumCRC - data[9] * 256;

        return data.slice(0, data[4] + 6);
    }

    /**
     * Parse unit response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     * @returns {Object} - Parsed unit info
     */
    _parseUnitResponse(msg, rinfo) {
        const posData = 7;
        const unitInfo = {
            ip: rinfo.address,
            id: `${msg[3]}.${msg[2]}.${msg[1]}.${msg[0]}`,
            type: this._getUnitType(msg[posData + 1]),
            barcode: this._parseBarcode(msg, posData + 2),
            canLoad: Boolean(msg[posData + 18]),
            hwVersion: `${msg[posData + 86]}.${(msg[posData + 85] >> 4)}.${(msg[posData + 85] & 0x0F)}`,
            fwVersion: `${msg[posData + 88]}.${msg[posData + 87]}.0`,
            manufactureDate: this._parseManufactureDate(msg, 19),
            recovery: Boolean(msg[posData + 89])
        };

        return unitInfo;
    }

    /**
     * Get unit type from mode byte
     * @param {number} mode - Mode byte
     * @returns {string} - Unit type
     */
    _getUnitType(mode) {
        switch (mode) {
            case 0: return 'Stand-Alone';
            case 1: return 'Slave';
            case 2: return 'Master';
            default: return 'Unknown';
        }
    }

    /**
     * Parse barcode from message
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Barcode string
     */
    _parseBarcode(msg, start) {
        let barcode = '';
        for (let i = 0; i < 16; i++) {
            barcode += String.fromCharCode(msg[start + i]);
        }
        return barcode.trim();
    }

    /**
     * Parse manufacture date from message
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Manufacture date string
     */
    _parseManufactureDate(msg, start) {
        let date = '';
        for (let i = 0; i < 8; i++) {
            date += (msg[start + i] - 48).toString();
        }
        return date;
    }

    /**
     * Handle UDP response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     */
    _handleResponse(msg, rinfo) {
        if (this.isScanning) {
            try {
                const unitInfo = this._parseUnitResponse(msg, rinfo);
                this.emit('unitFound', unitInfo);
            } catch (err) {
                console.error('Error parsing unit response:', err);
                this.emit('error', err);
            }
        }
    }

    /**
     * Scan network for units
     * @returns {Promise} - Promise that resolves when scan is complete
     */
    async scanNetwork() {
        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                this.isScanning = true;
                const message = this._createHardwareRequest('0.0.0.0');
                const broadcastAddress = '***************';

                this.client.send(message, this.UDP_PORT, broadcastAddress, (err) => {
                    if (err) {
                        this.isScanning = false;
                        reject(err);
                        return;
                    }

                    // Set timeout for scan
                    setTimeout(() => {
                        this.isScanning = false;
                        resolve();
                    }, 5000); // 5 second timeout
                });
            } catch (err) {
                this.isScanning = false;
                reject(err);
            }
        });
    }

    /**
     * Configure unit network settings
     * @param {Object} config - Unit configuration
     * @returns {Promise} - Promise that resolves when configuration is complete
     */
    async configureUnit(config) {
        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                const message = this._createConfigMessage(config);
                const targetAddress = config.ip;

                this.client.send(message, this.UDP_PORT, targetAddress, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // Set timeout for configuration
                    setTimeout(() => {
                        resolve();
                    }, 2000); // 2 second timeout
                });
            } catch (err) {
                reject(err);
            }
        });
    }

    /**
     * Create configuration message
     * @param {Object} config - Unit configuration
     * @returns {Buffer} - Configuration message
     */
    _createConfigMessage(config) {
        const data = Buffer.alloc(1024);
        const idParts = config.id.split('.');

        // Address bytes
        data[0] = parseInt(idParts[3]);
        data[1] = parseInt(idParts[2]);
        data[2] = parseInt(idParts[1]);
        data[3] = parseInt(idParts[0]);

        // Length and command will be set based on config type
        // This is a simplified version - you'll need to implement
        // specific message formats for different configuration types

        return data;
    }
}

module.exports = UDPService; 