import dgram from 'dgram';
import { EventEmitter } from 'events';
import os from 'os';

class UDPService extends EventEmitter {
    constructor() {
        super();
        this.UDP_PORT = 1234; // UDP port for sending to devices (same as C# app)
        this.LOCAL_UDP_PORT = 5678; // Local UDP port for receiving (same as C# app)
        this.client = null;
        this.isScanning = false;
        this.scanTimeout = null;

        // Barcode to device type mapping
        this.barcodeToType = {
            "8930000000019": "Room Logic Controller",
            "8930000000200": "Bedside-17T",
            "8930000100214": "Bedside-12T",
            "8930000100221": "BSP_R14_OL",
            "8930000000026": "RLC-I16",
            "8930000000033": "RLC-I20",
            "8930000200013": "RCU-32AO",
            "8930000200020": "RCU-8RL-24AO",
            "8930000200037": "RCU-16RL-16AO",
            "8930000200044": "RCU-24RL-8AO",
            "8930000210005": "RCU-11IN-4RL",
            "8930000210012": "RCU-21IN-10RL",
            "8930000210036": "RCU-30IN-10RL",
            "8930000210043": "RCU-48IN-16RL",
            "8930000210050": "RCU-48IN-16RL-4AO",
            "8930000210067": "RCU-48IN-16RL-4AI",
            "8930000210074": "RCU-48IN-16RL-K",
            "8930000210081": "RCU-48IN-16RL-DL",
            "8930000210111": "RCU-21IN-8RL",
            "8930000210128": "RCU-21IN-8RL-4AO",
            "8930000210135": "RCU-21IN-8RL-4AI",
            "8930000210142": "RCU-21IN-8RL-K",
            "8930000210159": "RCU-21IN-8RL-DL",
            "8930000200051": "GNT-EXT-6RL",
            "8930000200068": "GNT-EXT-8RL",
            "8930000200075": "GNT-EXT-10AO",
            "8930000200082": "GNT-EXT-28AO",
            "8930000200105": "GNT-EXT-12RL",
            "8930000200112": "GNT-EXT-20RL",
            "8930000200099": "GNT-EXT-12RL-12AO",
            "8930000220011": "GNT-EXT-24IN",
            "8930000220028": "GNT-EXT-48IN",
            "8930000230003": "GNT-ETH2KDL"
        };
    }

    /**
     * Get available network interfaces
     * @returns {Array} - Array of network interface info
     */
    getNetworkInterfaces() {
        const interfaces = os.networkInterfaces();
        const result = [];

        for (const [name, addresses] of Object.entries(interfaces)) {
            for (const addr of addresses) {
                if (addr.family === 'IPv4' && !addr.internal) {
                    result.push({
                        name,
                        address: addr.address,
                        netmask: addr.netmask,
                        broadcast: this._calculateBroadcast(addr.address, addr.netmask)
                    });
                }
            }
        }

        return result;
    }

    /**
     * Calculate broadcast address
     * @param {string} ip - IP address
     * @param {string} netmask - Network mask
     * @returns {string} - Broadcast address
     */
    _calculateBroadcast(ip, netmask) {
        const ipParts = ip.split('.').map(Number);
        const maskParts = netmask.split('.').map(Number);

        const broadcast = ipParts.map((part, index) => {
            return part | (255 - maskParts[index]);
        });

        return broadcast.join('.');
    }

    /**
     * Initialize UDP client
     */
    initialize() {
        this.client = dgram.createSocket('udp4');

        this.client.on('error', (err) => {
            console.error(`UDP Client error:\n${err.stack}`);
            this.client.close();
            this.emit('error', err);
        });

        this.client.on('message', (msg, rinfo) => {
            this._handleResponse(msg, rinfo);
        });

        // Enable broadcast before binding
        this.client.on('listening', () => {
            this.client.setBroadcast(true);
            console.log(`UDP client listening on port ${this.LOCAL_UDP_PORT} with broadcast enabled`);
        });

        this.client.bind(this.LOCAL_UDP_PORT);
    }

    /**
     * Close UDP client
     */
    close() {
        if (this.scanTimeout) {
            clearTimeout(this.scanTimeout);
            this.scanTimeout = null;
        }

        if (this.client) {
            this.client.close();
            this.client = null;
        }

        this.isScanning = false;
    }

    /**
     * Calculate CRC for message
     * @param {Buffer} data - Message data
     * @param {number} start - Start index
     * @param {number} length - Length of data to calculate CRC
     * @returns {number} - CRC value
     */
    _calculateCRC(data, start, length) {
        let sumCRC = 0;
        for (let i = start; i < start + length; i++) {
            sumCRC += data[i];
        }
        return sumCRC;
    }

    /**
     * Create hardware request message
     * @param {string} id - Unit ID (format: x.x.x.x)
     * @returns {Buffer} - Message buffer
     */
    _createHardwareRequest(id) {
        const idParts = id.split('.');
        const data = Buffer.alloc(1024);

        // Address bytes
        data[0] = parseInt(idParts[3] || '0');
        data[1] = parseInt(idParts[2] || '0');
        data[2] = parseInt(idParts[1] || '0');
        data[3] = parseInt(idParts[0] || '0');

        // Length
        data[4] = 4;  // Address:4 ;Length=2 byte; CMD:2, Data:0, CRC:2
        data[5] = 0;

        // Command
        data[6] = 1;  // Request hardware info
        data[7] = 4;

        // Calculate CRC
        const sumCRC = this._calculateCRC(data, 4, data[4] + 2);
        data[9] = Math.floor(sumCRC / 256);
        data[8] = sumCRC - data[9] * 256;

        return data.slice(0, data[4] + 6);
    }

    /**
     * Parse unit response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     * @returns {Object} - Parsed unit info
     */
    _parseUnitResponse(msg, rinfo) {
        try {
            // Validate message length
            if (!msg || msg.length < 100) {
                throw new Error(`Invalid message length: ${msg ? msg.length : 0}`);
            }

            const posData = 7;

            // Safely access buffer data with bounds checking
            const safeGet = (index, defaultValue = 0) => {
                return index < msg.length ? msg[index] : defaultValue;
            };

            // Parse barcode first to determine device type (exactly like C#)
            const barcode = this._parseBarcode(msg, posData + 70); // posData + 70 to posData + 83 (13 bytes)
            const deviceType = this._getDeviceTypeFromBarcode(barcode);

            // Parse Hardware_Enable byte (exactly like C#)
            const hardwareEnable = safeGet(posData + 84);
            const unitMode = this._getUnitMode(hardwareEnable);
            const canLoad = this._getCanLoad(hardwareEnable);
            const recovery = this._getRecovery(hardwareEnable);

            const unitInfo = {
                ip: rinfo.address,
                id: `${safeGet(3)}.${safeGet(2)}.${safeGet(1)}.${safeGet(0)}`,
                type: deviceType, // Use device type from barcode
                mode: unitMode, // Unit mode (Master/Slave/Stand-Alone)
                barcode: barcode,
                canLoad: canLoad, // Parse from Hardware_Enable byte
                hwVersion: `${safeGet(posData + 86)}.${(safeGet(posData + 85) >> 4)}.${(safeGet(posData + 85) & 0x0F)}`,
                fwVersion: `${safeGet(posData + 88)}.${safeGet(posData + 87)}.0`,
                manufactureDate: this._parseManufactureDate(msg, 19), // bytes 19 to 27
                recovery: recovery // Parse from Hardware_Enable byte
            };

            return unitInfo;
        } catch (error) {
            console.warn('Error parsing unit response:', error.message);
            // Return basic info if parsing fails
            return {
                ip: rinfo.address,
                id: 'Unknown',
                type: 'Unknown Device',
                mode: 'Unknown',
                barcode: '',
                canLoad: false,
                hwVersion: 'Unknown',
                fwVersion: 'Unknown',
                manufactureDate: '',
                recovery: false,
                parseError: error.message
            };
        }
    }

    /**
     * Get device type from barcode
     * @param {string} barcode - Device barcode
     * @returns {string} - Device type
     */
    _getDeviceTypeFromBarcode(barcode) {
        // Clean barcode (remove whitespace and non-printable characters)
        const cleanBarcode = barcode.trim().replace(/[^\x20-\x7E]/g, '');

        console.log(`Parsing barcode: "${barcode}" -> cleaned: "${cleanBarcode}"`);

        // Look up device type by barcode
        const deviceType = this.barcodeToType[cleanBarcode];

        if (deviceType) {
            console.log(`Found exact match: ${cleanBarcode} -> ${deviceType}`);
            return deviceType;
        }

        // If exact match not found, try partial matching for common patterns
        for (const [code, type] of Object.entries(this.barcodeToType)) {
            if (cleanBarcode.includes(code) || code.includes(cleanBarcode)) {
                console.log(`Found partial match: ${cleanBarcode} matches ${code} -> ${type}`);
                return type;
            }
        }

        console.log(`No match found for barcode: "${cleanBarcode}"`);
        return 'Unknown Device';
    }

    /**
     * Get unit mode from Hardware_Enable byte (exactly like C# version)
     * @param {number} hardwareEnable - Hardware_Enable byte
     * @returns {string} - Unit mode
     */
    _getUnitMode(hardwareEnable) {
        // C# logic:
        // int factor = 128;
        // for (int i = 1; i <= 5; i++) {
        //     if (Hardware_Enable >= factor) Hardware_Enable = Hardware_Enable - factor;
        //     factor = factor / 2;
        // }
        // if (Hardware_Enable >= 4) {
        //     Unit_CanLoad = 1;
        //     Hardware_Enable = Hardware_Enable - 4;
        // }
        // if (Hardware_Enable == 0) Unit_ActMode = "Stand-Alone";
        // else if (Hardware_Enable == 1) Unit_ActMode = "Slave";
        // else Unit_ActMode = "Master";

        let mode = hardwareEnable;

        // Remove higher bits (factor 128, 64, 32, 16, 8)
        let factor = 128;
        for (let i = 1; i <= 5; i++) {
            if (mode >= factor) mode = mode - factor;
            factor = Math.floor(factor / 2);
        }

        // Remove CanLoad bit (bit 2)
        if (mode >= 4) {
            mode = mode - 4;
        }

        // Determine mode
        if (mode === 0) return 'Stand-Alone';
        else if (mode === 1) return 'Slave';
        else return 'Master';
    }

    /**
     * Get CanLoad flag from Hardware_Enable byte (exactly like C# version)
     * @param {number} hardwareEnable - Hardware_Enable byte
     * @returns {boolean} - CanLoad flag
     */
    _getCanLoad(hardwareEnable) {
        // C# logic: if (Hardware_Enable >= 4) { Unit_CanLoad = 1; Hardware_Enable = Hardware_Enable - 4; }
        let mode = hardwareEnable;

        // Remove higher bits (factor 128, 64, 32, 16, 8)
        let factor = 128;
        for (let i = 1; i <= 5; i++) {
            if (mode >= factor) mode = mode - factor;
            factor = Math.floor(factor / 2);
        }

        // Check CanLoad bit (bit 2)
        return mode >= 4;
    }

    /**
     * Get Recovery flag from Hardware_Enable byte (exactly like C# version)
     * @param {number} hardwareEnable - Hardware_Enable byte
     * @returns {boolean} - Recovery flag
     */
    _getRecovery(hardwareEnable) {
        // C# logic: if ((Hardware_Enable & 0x40) == 0x40) recovery = true;
        return (hardwareEnable & 0x40) === 0x40;
    }

    /**
     * Parse barcode from message (exactly like C# version)
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Barcode string
     */
    _parseBarcode(msg, start) {
        try {
            let barcode = '';
            // C#: for (int i = posData + 70; i < posData + 83; i++) Unit_Barcode = Unit_Barcode + (DuLieuTuBo[i] - 48).ToString();
            for (let i = 0; i < 13 && (start + i) < msg.length; i++) { // 13 bytes barcode
                const value = msg[start + i];
                barcode += (value - 48).toString(); // Subtract 48 like C# version
            }
            return barcode;
        } catch (error) {
            console.warn('Error parsing barcode:', error.message);
            return '';
        }
    }

    /**
     * Parse manufacture date from message (exactly like C# version)
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Manufacture date string
     */
    _parseManufactureDate(msg, start) {
        try {
            let date = '';
            // C#: for (int i = 19; i < 27; i++) { man_date = man_date + (DuLieuTuBo[i] - 48).ToString(); }
            for (let i = 0; i < 8 && (start + i) < msg.length; i++) { // 8 bytes from position 19 to 27
                const value = msg[start + i];
                date += (value - 48).toString(); // Subtract 48 like C# version
            }
            return date;
        } catch (error) {
            console.warn('Error parsing manufacture date:', error.message);
            return '';
        }
    }

    /**
     * Handle UDP response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     */
    _handleResponse(msg, rinfo) {
        if (this.isScanning) {
            try {
                console.log(`Received response from ${rinfo.address}:${rinfo.port}, length: ${msg.length}`);

                // Validate message length like C#: if ((DuLieuTuBo[5] * 256 + DuLieuTuBo[4]) > 90)
                if (msg.length >= 6) {
                    const messageLength = msg[5] * 256 + msg[4];
                    if (messageLength > 90) {
                        const unitInfo = this._parseUnitResponse(msg, rinfo);

                        // Only emit if we got valid unit info
                        if (unitInfo && unitInfo.ip) {
                            this.emit('unitFound', unitInfo);
                            console.log(`Unit found: ${unitInfo.type} at ${unitInfo.ip}`);
                        }
                    } else {
                        console.log(`Message too short (${messageLength} <= 90), ignoring`);
                    }
                } else {
                    console.log(`Invalid message length: ${msg.length} < 6`);
                }
            } catch (err) {
                console.error('Error handling unit response:', err);
                // Don't emit error for individual parsing failures
                // Just log and continue
            }
        }
    }

    /**
     * Stop current network scan
     */
    stopScan() {
        if (this.scanTimeout) {
            clearTimeout(this.scanTimeout);
            this.scanTimeout = null;
        }
        this.isScanning = false;
        console.log('Network scan stopped');
    }

    /**
     * Scan network for units
     * @param {number} timeout - Scan timeout in milliseconds (default: 5000)
     * @returns {Promise} - Promise that resolves when scan is complete
     */
    async scanNetwork(timeout = 3000) { // Match C# timeout
        if (this.isScanning) {
            throw new Error('Scan already in progress');
        }

        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                this.isScanning = true;
                const message = this._createHardwareRequest('0.0.0.0');

                // Create separate socket for sending (like C# version)
                const sendClient = dgram.createSocket('udp4');

                sendClient.on('error', (err) => {
                    console.error('Send client error:', err);
                    sendClient.close();
                    this.isScanning = false;
                    reject(err);
                });

                // Enable broadcast without binding to specific port
                sendClient.setBroadcast(true);

                // Try multiple broadcast addresses to avoid permission issues
                const broadcastAddresses = ['***************'];

                // Add network-specific broadcast addresses
                const interfaces = this.getNetworkInterfaces();
                interfaces.forEach(iface => {
                    if (iface.broadcast && !broadcastAddresses.includes(iface.broadcast)) {
                        broadcastAddresses.push(iface.broadcast);
                    }
                });

                console.log(`Broadcasting to addresses: ${broadcastAddresses.join(', ')}:${this.UDP_PORT}`);

                let sendCount = 0;
                let successCount = 0;
                let errors = [];

                const sendToAddress = (address) => {
                    sendClient.send(message, this.UDP_PORT, address, (err) => {
                        sendCount++;

                        if (err) {
                            console.warn(`Failed to send to ${address}:`, err.message);
                            errors.push({ address, error: err.message });
                        } else {
                            console.log(`Successfully sent to ${address}`);
                            successCount++;
                        }

                        // Check if all sends are complete
                        if (sendCount === broadcastAddresses.length) {
                            sendClient.close(); // Close send client after all sends

                            // If all sends failed, reject
                            if (successCount === 0) {
                                console.error('All broadcast attempts failed:', errors);
                                this.isScanning = false;
                                reject(new Error(`All broadcast attempts failed: ${errors.map(e => `${e.address}: ${e.error}`).join(', ')}`));
                                return;
                            }

                            console.log(`Broadcast sent successfully to ${successCount}/${broadcastAddresses.length} addresses, waiting for responses...`);

                            // Set timeout for scan completion (like C# 3000ms timeout)
                            this.scanTimeout = setTimeout(() => {
                                this.isScanning = false;
                                this.scanTimeout = null;
                                console.log('Scan timeout reached, finishing scan');
                                resolve();
                            }, timeout);
                        }
                    });
                };

                // Send to all broadcast addresses
                broadcastAddresses.forEach(sendToAddress);

            } catch (err) {
                this.isScanning = false;
                reject(err);
            }
        });
    }

    /**
     * Configure unit network settings
     * @param {Object} config - Unit configuration
     * @returns {Promise} - Promise that resolves when configuration is complete
     */
    async configureUnit(config) {
        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                const message = this._createConfigMessage(config);
                const targetAddress = config.ip;

                this.client.send(message, this.UDP_PORT, targetAddress, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // Set timeout for configuration
                    setTimeout(() => {
                        resolve();
                    }, 2000); // 2 second timeout
                });
            } catch (err) {
                reject(err);
            }
        });
    }

    /**
     * Create configuration message
     * @param {Object} config - Unit configuration
     * @returns {Buffer} - Configuration message
     */
    _createConfigMessage(config) {
        const data = Buffer.alloc(1024);
        const idParts = config.id.split('.');

        // Address bytes
        data[0] = parseInt(idParts[3]);
        data[1] = parseInt(idParts[2]);
        data[2] = parseInt(idParts[1]);
        data[3] = parseInt(idParts[0]);

        // Length and command will be set based on config type
        // This is a simplified version - you'll need to implement
        // specific message formats for different configuration types

        return data;
    }
}

export default UDPService;