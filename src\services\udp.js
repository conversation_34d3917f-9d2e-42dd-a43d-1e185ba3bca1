import dgram from 'dgram';
import { EventEmitter } from 'events';
import os from 'os';

class UDPService extends EventEmitter {
    constructor() {
        super();
        this.UDP_PORT = 5000; // Default UDP port
        this.LOCAL_UDP_PORT = 5001; // Local UDP port for receiving
        this.client = null;
        this.isScanning = false;
        this.scanTimeout = null;

        // Barcode to device type mapping
        this.barcodeToType = {
            "8930000000019": "Room Logic Controller",
            "8930000000200": "Bedside-17T",
            "8930000100214": "Bedside-12T",
            "8930000100221": "BSP_R14_OL",
            "8930000000026": "RLC-I16",
            "8930000000033": "RLC-I20",
            "8930000200013": "RCU-32AO",
            "8930000200020": "RCU-8RL-24AO",
            "8930000200037": "RCU-16RL-16AO",
            "8930000200044": "RCU-24RL-8AO",
            "8930000210005": "RCU-11IN-4RL",
            "8930000210012": "RCU-21IN-10RL",
            "8930000210036": "RCU-30IN-10RL",
            "8930000210043": "RCU-48IN-16RL",
            "8930000210050": "RCU-48IN-16RL-4AO",
            "8930000210067": "RCU-48IN-16RL-4AI",
            "8930000210074": "RCU-48IN-16RL-K",
            "8930000210081": "RCU-48IN-16RL-DL",
            "8930000210111": "RCU-21IN-8RL",
            "8930000210128": "RCU-21IN-8RL-4AO",
            "8930000210135": "RCU-21IN-8RL-4AI",
            "8930000210142": "RCU-21IN-8RL-K",
            "8930000210159": "RCU-21IN-8RL-DL",
            "8930000200051": "GNT-EXT-6RL",
            "8930000200068": "GNT-EXT-8RL",
            "8930000200075": "GNT-EXT-10AO",
            "8930000200082": "GNT-EXT-28AO",
            "8930000200105": "GNT-EXT-12RL",
            "8930000200112": "GNT-EXT-20RL",
            "8930000200099": "GNT-EXT-12RL-12AO",
            "8930000220011": "GNT-EXT-24IN",
            "8930000220028": "GNT-EXT-48IN",
            "8930000230003": "GNT-ETH2KDL"
        };
    }

    /**
     * Get available network interfaces
     * @returns {Array} - Array of network interface info
     */
    getNetworkInterfaces() {
        const interfaces = os.networkInterfaces();
        const result = [];

        for (const [name, addresses] of Object.entries(interfaces)) {
            for (const addr of addresses) {
                if (addr.family === 'IPv4' && !addr.internal) {
                    result.push({
                        name,
                        address: addr.address,
                        netmask: addr.netmask,
                        broadcast: this._calculateBroadcast(addr.address, addr.netmask)
                    });
                }
            }
        }

        return result;
    }

    /**
     * Calculate broadcast address
     * @param {string} ip - IP address
     * @param {string} netmask - Network mask
     * @returns {string} - Broadcast address
     */
    _calculateBroadcast(ip, netmask) {
        const ipParts = ip.split('.').map(Number);
        const maskParts = netmask.split('.').map(Number);

        const broadcast = ipParts.map((part, index) => {
            return part | (255 - maskParts[index]);
        });

        return broadcast.join('.');
    }

    /**
     * Initialize UDP client
     */
    initialize() {
        this.client = dgram.createSocket('udp4');

        this.client.on('error', (err) => {
            console.error(`UDP Client error:\n${err.stack}`);
            this.client.close();
            this.emit('error', err);
        });

        this.client.on('message', (msg, rinfo) => {
            this._handleResponse(msg, rinfo);
        });

        this.client.bind(this.LOCAL_UDP_PORT);
    }

    /**
     * Close UDP client
     */
    close() {
        if (this.scanTimeout) {
            clearTimeout(this.scanTimeout);
            this.scanTimeout = null;
        }

        if (this.client) {
            this.client.close();
            this.client = null;
        }

        this.isScanning = false;
    }

    /**
     * Calculate CRC for message
     * @param {Buffer} data - Message data
     * @param {number} start - Start index
     * @param {number} length - Length of data to calculate CRC
     * @returns {number} - CRC value
     */
    _calculateCRC(data, start, length) {
        let sumCRC = 0;
        for (let i = start; i < start + length; i++) {
            sumCRC += data[i];
        }
        return sumCRC;
    }

    /**
     * Create hardware request message
     * @param {string} id - Unit ID (format: x.x.x.x)
     * @returns {Buffer} - Message buffer
     */
    _createHardwareRequest(id) {
        const idParts = id.split('.');
        const data = Buffer.alloc(1024);

        // Address bytes
        data[0] = parseInt(idParts[3] || '0');
        data[1] = parseInt(idParts[2] || '0');
        data[2] = parseInt(idParts[1] || '0');
        data[3] = parseInt(idParts[0] || '0');

        // Length
        data[4] = 4;  // Address:4 ;Length=2 byte; CMD:2, Data:0, CRC:2
        data[5] = 0;

        // Command
        data[6] = 1;  // Request hardware info
        data[7] = 4;

        // Calculate CRC
        const sumCRC = this._calculateCRC(data, 4, data[4] + 2);
        data[9] = Math.floor(sumCRC / 256);
        data[8] = sumCRC - data[9] * 256;

        return data.slice(0, data[4] + 6);
    }

    /**
     * Parse unit response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     * @returns {Object} - Parsed unit info
     */
    _parseUnitResponse(msg, rinfo) {
        try {
            // Validate message length
            if (!msg || msg.length < 100) {
                throw new Error(`Invalid message length: ${msg ? msg.length : 0}`);
            }

            const posData = 7;

            // Safely access buffer data with bounds checking
            const safeGet = (index, defaultValue = 0) => {
                return index < msg.length ? msg[index] : defaultValue;
            };

            // Parse barcode first to determine device type
            const barcode = this._parseBarcode(msg, posData + 2);
            const deviceType = this._getDeviceTypeFromBarcode(barcode);
            const unitMode = this._getUnitMode(safeGet(posData + 1));

            const unitInfo = {
                ip: rinfo.address,
                id: `${safeGet(3)}.${safeGet(2)}.${safeGet(1)}.${safeGet(0)}`,
                type: deviceType, // Use device type from barcode
                mode: unitMode, // Unit mode (Master/Slave/Stand-Alone)
                barcode: barcode,
                canLoad: Boolean(safeGet(posData + 18)),
                hwVersion: `${safeGet(posData + 86)}.${(safeGet(posData + 85) >> 4)}.${(safeGet(posData + 85) & 0x0F)}`,
                fwVersion: `${safeGet(posData + 88)}.${safeGet(posData + 87)}.0`,
                manufactureDate: this._parseManufactureDate(msg, posData + 19),
                recovery: Boolean(safeGet(posData + 89))
            };

            return unitInfo;
        } catch (error) {
            console.warn('Error parsing unit response:', error.message);
            // Return basic info if parsing fails
            return {
                ip: rinfo.address,
                id: 'Unknown',
                type: 'Unknown Device',
                mode: 'Unknown',
                barcode: '',
                canLoad: false,
                hwVersion: 'Unknown',
                fwVersion: 'Unknown',
                manufactureDate: '',
                recovery: false,
                parseError: error.message
            };
        }
    }

    /**
     * Get device type from barcode
     * @param {string} barcode - Device barcode
     * @returns {string} - Device type
     */
    _getDeviceTypeFromBarcode(barcode) {
        // Clean barcode (remove whitespace and non-printable characters)
        const cleanBarcode = barcode.trim().replace(/[^\x20-\x7E]/g, '');

        console.log(`Parsing barcode: "${barcode}" -> cleaned: "${cleanBarcode}"`);

        // Look up device type by barcode
        const deviceType = this.barcodeToType[cleanBarcode];

        if (deviceType) {
            console.log(`Found exact match: ${cleanBarcode} -> ${deviceType}`);
            return deviceType;
        }

        // If exact match not found, try partial matching for common patterns
        for (const [code, type] of Object.entries(this.barcodeToType)) {
            if (cleanBarcode.includes(code) || code.includes(cleanBarcode)) {
                console.log(`Found partial match: ${cleanBarcode} matches ${code} -> ${type}`);
                return type;
            }
        }

        console.log(`No match found for barcode: "${cleanBarcode}"`);
        return 'Unknown Device';
    }

    /**
     * Get unit mode from mode byte
     * @param {number} mode - Mode byte
     * @returns {string} - Unit mode
     */
    _getUnitMode(mode) {
        switch (mode) {
            case 0: return 'Stand-Alone';
            case 1: return 'Slave';
            case 2: return 'Master';
            default: return 'Unknown';
        }
    }

    /**
     * Parse barcode from message
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Barcode string
     */
    _parseBarcode(msg, start) {
        try {
            let barcode = '';
            for (let i = 0; i < 16 && (start + i) < msg.length; i++) {
                const charCode = msg[start + i];
                // Only add printable ASCII characters
                if (charCode >= 32 && charCode <= 126) {
                    barcode += String.fromCharCode(charCode);
                }
            }
            return barcode.trim();
        } catch (error) {
            console.warn('Error parsing barcode:', error.message);
            return '';
        }
    }

    /**
     * Parse manufacture date from message
     * @param {Buffer} msg - Message buffer
     * @param {number} start - Start index
     * @returns {string} - Manufacture date string
     */
    _parseManufactureDate(msg, start) {
        try {
            let date = '';
            for (let i = 0; i < 8 && (start + i) < msg.length; i++) {
                const value = msg[start + i];
                // Validate that it's a valid digit character
                if (value >= 48 && value <= 57) { // ASCII '0' to '9'
                    date += String.fromCharCode(value);
                }
            }
            return date;
        } catch (error) {
            console.warn('Error parsing manufacture date:', error.message);
            return '';
        }
    }

    /**
     * Handle UDP response
     * @param {Buffer} msg - Response message
     * @param {Object} rinfo - Remote info
     */
    _handleResponse(msg, rinfo) {
        if (this.isScanning) {
            try {
                console.log(`Received response from ${rinfo.address}:${rinfo.port}, length: ${msg.length}`);
                const unitInfo = this._parseUnitResponse(msg, rinfo);

                // Only emit if we got valid unit info
                if (unitInfo && unitInfo.ip) {
                    this.emit('unitFound', unitInfo);
                    console.log(`Unit found: ${unitInfo.type} at ${unitInfo.ip}`);
                }
            } catch (err) {
                console.error('Error handling unit response:', err);
                // Don't emit error for individual parsing failures
                // Just log and continue
            }
        }
    }

    /**
     * Stop current network scan
     */
    stopScan() {
        if (this.scanTimeout) {
            clearTimeout(this.scanTimeout);
            this.scanTimeout = null;
        }
        this.isScanning = false;
        console.log('Network scan stopped');
    }

    /**
     * Scan network for units
     * @param {number} timeout - Scan timeout in milliseconds (default: 5000)
     * @returns {Promise} - Promise that resolves when scan is complete
     */
    async scanNetwork(timeout = 5000) {
        if (this.isScanning) {
            throw new Error('Scan already in progress');
        }

        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                this.isScanning = true;
                const message = this._createHardwareRequest('0.0.0.0');

                // Get network interfaces and send to each broadcast address
                const interfaces = this.getNetworkInterfaces();
                const broadcastAddresses = interfaces.length > 0
                    ? interfaces.map(iface => iface.broadcast)
                    : ['***************']; // Fallback to global broadcast

                console.log('Broadcasting to addresses:', broadcastAddresses);

                let sendCount = 0;
                let sendErrors = [];

                const sendToAddress = (address) => {
                    this.client.send(message, this.UDP_PORT, address, (err) => {
                        sendCount++;

                        if (err) {
                            console.warn(`Failed to send to ${address}:`, err.message);
                            sendErrors.push({ address, error: err.message });
                        }

                        // Check if all sends are complete
                        if (sendCount === broadcastAddresses.length) {
                            // Set timeout for scan completion
                            this.scanTimeout = setTimeout(() => {
                                this.isScanning = false;
                                this.scanTimeout = null;

                                if (sendErrors.length === broadcastAddresses.length) {
                                    // All sends failed
                                    reject(new Error(`Failed to send to all addresses: ${sendErrors.map(e => e.error).join(', ')}`));
                                } else {
                                    resolve();
                                }
                            }, timeout);
                        }
                    });
                };

                // Send to all broadcast addresses
                broadcastAddresses.forEach(sendToAddress);

            } catch (err) {
                this.isScanning = false;
                reject(err);
            }
        });
    }

    /**
     * Configure unit network settings
     * @param {Object} config - Unit configuration
     * @returns {Promise} - Promise that resolves when configuration is complete
     */
    async configureUnit(config) {
        if (!this.client) {
            this.initialize();
        }

        return new Promise((resolve, reject) => {
            try {
                const message = this._createConfigMessage(config);
                const targetAddress = config.ip;

                this.client.send(message, this.UDP_PORT, targetAddress, (err) => {
                    if (err) {
                        reject(err);
                        return;
                    }

                    // Set timeout for configuration
                    setTimeout(() => {
                        resolve();
                    }, 2000); // 2 second timeout
                });
            } catch (err) {
                reject(err);
            }
        });
    }

    /**
     * Create configuration message
     * @param {Object} config - Unit configuration
     * @returns {Buffer} - Configuration message
     */
    _createConfigMessage(config) {
        const data = Buffer.alloc(1024);
        const idParts = config.id.split('.');

        // Address bytes
        data[0] = parseInt(idParts[3]);
        data[1] = parseInt(idParts[2]);
        data[2] = parseInt(idParts[1]);
        data[3] = parseInt(idParts[0]);

        // Length and command will be set based on config type
        // This is a simplified version - you'll need to implement
        // specific message formats for different configuration types

        return data;
    }
}

export default UDPService;